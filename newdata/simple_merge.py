#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单CSV合并脚本
快速合并data2.csv和data22.csv
"""

import pandas as pd
import os

def simple_merge():
    """
    简单快速的CSV合并
    """
    print("开始合并CSV文件...")
    
    # 文件路径
    file1 = "/Users/<USER>/Project/data/newdata/data2.csv"
    file2 = "/Users/<USER>/Project/data/newdata/data22.csv"
    output = "/Users/<USER>/Project/data/newdata/merged_data.csv"
    
    try:
        # 读取文件
        print(f"读取 {file1}...")
        df1 = pd.read_csv(file1)
        print(f"  - {len(df1):,} 行")
        
        print(f"读取 {file2}...")
        df2 = pd.read_csv(file2)
        print(f"  - {len(df2):,} 行")
        
        # 合并
        print("合并数据...")
        merged = pd.concat([df1, df2], ignore_index=True)
        print(f"  - 合并后: {len(merged):,} 行")
        
        # 保存
        print(f"保存到 {output}...")
        merged.to_csv(output, index=False)
        
        # 文件大小
        size_mb = os.path.getsize(output) / (1024 * 1024)
        print(f"完成! 输出文件大小: {size_mb:.2f} MB")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    simple_merge()

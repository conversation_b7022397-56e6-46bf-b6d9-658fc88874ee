#!/usr/bin/env python3
"""
删除指定case_id的数据行
"""

import pandas as pd
import os
from datetime import datetime

# 配置
LABEL_PATH = "/Users/<USER>/Project/data/newdata/merged_data.csv"

def delete_case_ids(case_ids_to_delete, backup=True):
    """
    删除指定的case_id数据行
    
    Args:
        case_ids_to_delete (list): 要删除的case_id列表
        backup (bool): 是否创建备份文件
    """
    
    print(f"正在读取数据文件: {LABEL_PATH}")
    
    # 检查文件是否存在
    if not os.path.exists(LABEL_PATH):
        print(f"错误：文件 {LABEL_PATH} 不存在")
        return False
    
    # 读取数据
    try:
        df = pd.read_csv(LABEL_PATH)
        print(f"原始数据量: {len(df)} 条")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return False
    
    # 检查case_id列是否存在
    if 'case_id' not in df.columns:
        print("错误：数据中没有找到 'case_id' 列")
        print(f"可用列: {list(df.columns)}")
        return False
    
    # 创建备份（如果需要）
    if backup:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = LABEL_PATH.replace('.csv', f'_backup_{timestamp}.csv')
        df.to_csv(backup_path, index=False)
        print(f"已创建备份文件: {backup_path}")
    
    # 检查要删除的case_id是否存在
    existing_case_ids = set(df['case_id'].astype(str))
    case_ids_to_delete_str = [str(cid) for cid in case_ids_to_delete]
    
    found_case_ids = []
    not_found_case_ids = []
    
    for case_id in case_ids_to_delete_str:
        if case_id in existing_case_ids:
            found_case_ids.append(case_id)
        else:
            not_found_case_ids.append(case_id)
    
    # 显示检查结果
    print(f"\n=== 删除前检查 ===")
    print(f"要删除的case_id数量: {len(case_ids_to_delete_str)}")
    print(f"找到的case_id: {len(found_case_ids)}")
    print(f"未找到的case_id: {len(not_found_case_ids)}")
    
    if found_case_ids:
        print(f"\n找到的case_id:")
        for cid in found_case_ids:
            print(f"  - {cid}")
    
    if not_found_case_ids:
        print(f"\n未找到的case_id:")
        for cid in not_found_case_ids:
            print(f"  - {cid}")
    
    if not found_case_ids:
        print("没有找到任何要删除的case_id，操作取消")
        return False
    
    # 执行删除操作
    print(f"\n=== 执行删除操作 ===")
    original_count = len(df)
    
    # 删除指定的case_id行
    df_filtered = df[~df['case_id'].astype(str).isin(found_case_ids)]
    deleted_count = original_count - len(df_filtered)
    
    print(f"删除前数据量: {original_count}")
    print(f"删除后数据量: {len(df_filtered)}")
    print(f"实际删除数量: {deleted_count}")
    
    # 保存修改后的数据
    try:
        df_filtered.to_csv(LABEL_PATH, index=False)
        print(f"✅ 数据已保存到: {LABEL_PATH}")
        return True
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False

def main():
    """主函数"""
    
    # 要删除的case_id列表
    case_ids_to_delete = [
        "MT0087920250629142800S05T09",
        "MT0341120250609094052S05T09",
        "MT0477520250701094544S05T09",
        "MT0487120250629090952S05T09",
        "MT0503320250626093537S05T09",
        "MT0519120250509093232S05T09",
        "MT0836220250509140920S05T09",
        "MT0836220250523144452S05T09",
        "MT0860120250510095048S05T09"



    ]
    
    print("=" * 60)
    print("删除指定case_id数据工具")
    print("=" * 60)
    
    print(f"要删除的case_id:")
    for i, case_id in enumerate(case_ids_to_delete, 1):
        print(f"  {i}. {case_id}")
    
    # 确认操作
    confirm = input(f"\n确认删除以上 {len(case_ids_to_delete)} 个case_id吗？(y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        success = delete_case_ids(case_ids_to_delete, backup=True)
        if success:
            print("\n✅ 删除操作完成！")
        else:
            print("\n❌ 删除操作失败！")
    else:
        print("\n操作已取消")

if __name__ == "__main__":
    main()
